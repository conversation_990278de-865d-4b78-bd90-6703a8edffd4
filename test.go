/*
This is watch_media batch.
New config:
        ./start.sh -n watchMedia -cmd "batch/watch_media.go force dryrun"
createDate:    2025-05-09
Author:        Maggie
Run frequency: when need
*/

package main

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"os/signal"
	"strings"
	"sync"
	"syscall"
	"time"

	"goapp/batch_base"

	golog "github.com/real-rm/golog"
	gomongo "github.com/real-rm/gomongo"
	"github.com/real-rm/goprocess"
	"github.com/real-rm/gowatch"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

var (
	gWatchedObject   *gowatch.WatchObject
	gUpdateSysData   gowatch.UpdateSysDataFunc
	SysdataCol       *gomongo.MongoCollection
	ResoTrebMediaCol *gomongo.MongoCollection
	gProcessMonitor  *goprocess.ProcessMonitor
	dryRun           bool
)

func init() {
	// Initialize base[config, logging]
	if err := batch_base.InitBase(); err != nil {
		golog.Fatalf("Failed to initialize base: %v", err)
	}

	// Initialize MongoDB last (after config is loaded)
	if err := gomongo.InitMongoDB(); err != nil {
		golog.Fatalf("Failed to initialize MongoDB: %v", err)
	}

	SysdataCol = gomongo.Coll("rni", "sysdata")
	ResoTrebMediaCol = gomongo.Coll("rni", "reso_treb_evow_media")
	gUpdateSysData = gowatch.GetUpdateSysdataFunction(SysdataCol, PROCESS_STATUS_ID)

	// // Get current file name
	// _, currentFile, _, _ := runtime.Caller(0)
	// fileName := filepath.Base(currentFile)

	// Initialize ProcessMonitor
	var err error
	gProcessMonitor, err = goprocess.NewProcessMonitor(goprocess.ProcessMonitorOptions{
		ProcessStatusCol: gomongo.Coll("rni", "processStatus"),
		UpdateInterval:   time.Duration(UPDATE_PROCESS_STATE_INTERVAL) * time.Millisecond,
		Multiplier:       1.5, // Allow 50% more time than the update interval
	})
	if err != nil {
		golog.Fatalf("Failed to initialize ProcessMonitor: %v", err)
	}
	// handleExitFn = gProcessMonitor.GetHandleExitFn(goprocess.Exit, gUpdateSysData).(func(map[string]interface{}))
}

const (
	HALF_DAY_IN_MS                = 12 * 3600 * 1000
	UPDATE_PROCESS_STATE_INTERVAL = 10 * 60 * 1000 // 10 minutes
	PROCESS_STATUS_ID             = "watchResoTrebMedia"
	DEFAULT_BATCH_USER_ID         = "batch"
)

// ProcessInsert handles property insertion
func ProcessInsert(changeDoc bson.M, prop bson.M) error {
	if prop["_id"] == nil {
		golog.Error("invalid prop: no id")
		return fmt.Errorf("no id")
	}
	if dryRun {
		golog.Info("dryRun", "propId", prop["_id"])
	}
	golog.Info("Watch_example: Process insert", "propId", prop["_id"], "changeDoc", changeDoc)
	return nil
}

// ProcessReplace handles property replacement
func ProcessReplace(changeDoc bson.M, prop bson.M) error {
	if prop["_id"] == nil {
		golog.Error("invalid prop: no id")
		return fmt.Errorf("no id")
	}
	golog.Info("Watch_example: Process replace", "propId", prop["_id"], "changeDoc", changeDoc)
	return nil
}

// ProcessUpdate handles property updates
func ProcessUpdate(changeDoc bson.M, prop bson.M) error {
	if prop["_id"] == nil {
		golog.Error("invalid prop: no id")
		return fmt.Errorf("no id")
	}
	golog.Info("Watch_example: Process update", "propId", prop["_id"], "changeDoc", changeDoc)
	return nil
}

// ProcessDelete handles property deletion
func ProcessDelete(changeDoc bson.M, id interface{}) error {
	golog.Info("Watch_example: Process delete", "id", id, "changeDoc", changeDoc)
	return nil
}

// UpdateProcessStatus updates the process status
func UpdateProcessStatus(startTs time.Time) error {
	golog.Info("UpdateProcessStatus", "startTs", startTs)
	opts := goprocess.UpdateProcessStatusOptions{
		Status:   goprocess.RunningNormal,
		StartTs:  &startTs,
		ErrorMsg: nil,
		Stats:    nil,
	}
	return gProcessMonitor.UpdateProcessStatus(opts)
}

var (
	lastTokenPrintTs = time.Now()
	gStat            = make(map[string]interface{})
	tokenMu          sync.Mutex
)

// OnTokenUpdate handles token updates
func OnTokenUpdate(tokenOpt gowatch.TokenUpdateOptions) error {
	tokenMu.Lock()
	defer tokenMu.Unlock()

	golog.Info("onTokenUpdate", "token", tokenOpt.Token, "gStat", gStat, "from:", lastTokenPrintTs)
	lastTokenPrintTs = time.Now()
	gStat = make(map[string]interface{})
	updateFields := gowatch.UpdateFields{
		Token:          tokenOpt.Token,
		TokenClusterTs: tokenOpt.TokenClusterTs,
		TokenChangeTs:  tokenOpt.TokenChangeTs,
		Status:         "running",
		ResumeMt:       tokenOpt.ResumeMt,
	}
	return gUpdateSysData(updateFields)
}

// OnChange handles property changes
func OnChange(changeDoc bson.M, coll *gomongo.MongoCollection) error {
	if gWatchedObject == nil {
		golog.Error("gWatchedObject is nil")
		return fmt.Errorf("gWatchedObject is nil")
	}
	return gowatch.ProcessChangedObject(gowatch.ProcessChangedObjectOptions{
		ChangedObj:  changeDoc,
		WatchedColl: coll,
		DeleteOneFn: func(id interface{}) error {
			return ProcessDelete(changeDoc, id)
		},
		InsertOneFn: func(prop bson.M) error {
			return ProcessInsert(changeDoc, prop)
		},
		ReplaceOneFn: func(prop bson.M) error {
			return ProcessReplace(changeDoc, prop)
		},
		UpdateOneFn: func(prop bson.M) error {
			return ProcessUpdate(changeDoc, prop)
		},
		WatchedStream: gWatchedObject,
	})
}

// WatchProp sets up the watch on properties collection
func WatchProp(ctx context.Context, cancel context.CancelFunc, token *bson.M, tokenClusterTs time.Time, resumeMt ...time.Time) error {
	// Use background context for watch operation
	var startTs time.Time
	if len(resumeMt) > 0 && !resumeMt[0].IsZero() {
		startTs = resumeMt[0]
	} else {
		startTs = time.Now()
		if !tokenClusterTs.IsZero() {
			golog.Debug("tokenClusterTs is not zero", "tokenClusterTs", tokenClusterTs)
			startTs = tokenClusterTs
		}
	}

	query := bson.M{"mt": bson.M{"$gte": startTs}}
	opt := gowatch.WatchOptions{
		WatchedColl:           ResoTrebMediaCol,
		OnChange:              OnChange,
		OnTokenUpdate:         OnTokenUpdate,
		QueryWhenInvalidToken: query,
		SavedToken:            token,
		HighWaterMark:         20,
		OnError: func(err error) {
			golog.Error("watchProp error", "err", err.Error())
			GracefulExit(err)
		},
		UpdateProcessStatusFn: func() error {
			return UpdateProcessStatus(time.Now())
		},
		UpdateTokenTimerS: 120,
		Context:           ctx,
		Cancel:            cancel,
	}

	watchObj, err := gowatch.WatchTarget(opt)
	if err != nil {
		golog.Error("watchProp error", "err", err)
		GracefulExit(err)
	}
	gWatchedObject = watchObj
	return nil
}

// GetTokenAndWatch gets a new token and starts watching
func GetTokenAndWatch(ctx context.Context, cancel context.CancelFunc) error {
	token, tokenClusterTs, err := gowatch.GetToken(ResoTrebMediaCol)
	if err != nil {
		GracefulExit(err)
		return err
	}

	tokenOpt := gowatch.TokenUpdateOptions{
		Token:          token,
		TokenClusterTs: tokenClusterTs,
	}
	if err := OnTokenUpdate(tokenOpt); err != nil {
		GracefulExit(err)
		return err
	}
	golog.Debug("###token", "token", token)
	return WatchProp(ctx, cancel, token, tokenClusterTs)
}

// GetSignalChan returns a channel that informs about pressing Ctrl+C
func GetSignalChan() chan os.Signal {
	signalChannel := make(chan os.Signal, 1)
	signal.Notify(signalChannel,
		syscall.SIGHUP,
		syscall.SIGINT,
		syscall.SIGTERM,
		syscall.SIGQUIT)
	return signalChannel
}

// GracefulExit handles graceful shutdown
func GracefulExit(err error) {
	if err != nil {
		golog.Error("gracefulExit error", "err", err.Error())
	}

	params := map[string]interface{}{
		"watchedObject": gWatchedObject,
		"exitFn":        batch_base.Exit,
	}
	if err != nil {
		params["error"] = err.Error()
	}

	if gWatchedObject != nil {
		if err := gWatchedObject.FinishAndUpdateSysdata(gUpdateSysData); err != nil {
			golog.Error("FinishAndUpdateSysdata error", "err", err.Error())
		}
	}

	errMsg := func() string {
		if err != nil {
			return err.Error()
		}
		return ""
	}()

	gProcessMonitor.UpdateStatusWhenAllFinish(errMsg, func() {
		golog.Info("UpdateStatusWhenAllFinish", "err", errMsg)
	})

}

// handleTokenAndWatch handles token retrieval and watching logic
func handleTokenAndWatch(watchCtx context.Context, watchCancel context.CancelFunc, sysData bson.M, startTs time.Time) error {
	token := sysData["token"]

	var tokenClusterTs primitive.DateTime
	if v, ok := sysData["tokenClusterTs"].(primitive.DateTime); ok {
		tokenClusterTs = v
	}

	var resumeMt time.Time
	if v, ok := sysData["resumeMt"].(primitive.DateTime); ok {
		resumeMt = v.Time()
	}

	// If no token or token is too old, start fresh
	if token == nil || (resumeMt.IsZero() && !tokenClusterTs.Time().After(startTs.Add(-HALF_DAY_IN_MS*time.Millisecond))) {
		golog.Info("Starting fresh watch")
		return GetTokenAndWatch(watchCtx, watchCancel)
	}

	// Parse and validate existing token
	var parsedToken bson.M
	if err := json.Unmarshal([]byte(token.(string)), &parsedToken); err != nil {
		golog.Info("Invalid token, starting fresh", "token", token)
		return GetTokenAndWatch(watchCtx, watchCancel)
	}

	// Use existing token
	golog.Info("Continuing watch from existing token", "tokenClusterTs", tokenClusterTs)
	tokenOpt := gowatch.TokenUpdateOptions{
		Token:          &parsedToken,
		TokenClusterTs: tokenClusterTs.Time(),
	}
	if err := OnTokenUpdate(tokenOpt); err != nil {
		GracefulExit(err)
		return err
	}
	return WatchProp(watchCtx, watchCancel, &parsedToken, tokenClusterTs.Time(), resumeMt)
}

func main() {
	// Check for force parameter
	isForce := false
	dryRun = false
	for _, arg := range os.Args {
		switch arg {
		case "force":
			isForce = true
		case "dryrun":
			dryRun = true
		}
	}
	golog.Info("main", "force", isForce, "dryRun", dryRun)

	// Set up signal handling
	signalChannel := GetSignalChan()
	ctx, cancelCtx := context.WithCancel(context.Background())
	watchCtx, watchCancel := context.WithCancel(context.Background())

	// Handle signals
	go func() {
		sig := <-signalChannel
		sigName := "SIG" + strings.ToUpper(strings.TrimPrefix(sig.String(), "SIG"))
		errMsg := fmt.Sprintf("%s received %s", PROCESS_STATUS_ID, sigName)
		golog.Error(errMsg)
		GracefulExit(fmt.Errorf(errMsg))
		signal.Stop(signalChannel)
		close(signalChannel)
		cancelCtx()
	}()

	// Check for running process
	isRunning, err := gProcessMonitor.CheckRunningProcess()
	if err != nil {
		// Only log error if it's not a "no documents" error
		if !strings.Contains(err.Error(), "no documents") {
			golog.Error("Failed to check running process",
				"error", err,
				"error_type", fmt.Sprintf("%T", err),
				"error_string", err.Error(),
				"process_id", PROCESS_STATUS_ID)
			GracefulExit(fmt.Errorf("failed to check running process: %v", err))
		}
		golog.Info("No existing process found, starting fresh", "process_id", PROCESS_STATUS_ID)
	}
	if isRunning && !isForce {
		golog.Warn("Process is already running", "process_id", PROCESS_STATUS_ID)
		GracefulExit(fmt.Errorf(goprocess.HasRunningProcess))
	}

	// Update process status
	startTs := time.Now()
	if err := UpdateProcessStatus(startTs); err != nil {
		golog.Error("Failed to update process status",
			"error", err,
			"error_type", fmt.Sprintf("%T", err),
			"error_string", err.Error(),
			"process_id", PROCESS_STATUS_ID)
		GracefulExit(fmt.Errorf("failed to update process status: %v", err))
	}

	// Get last resume token
	var sysData bson.M
	err = SysdataCol.FindOne(
		context.Background(),
		bson.M{"_id": PROCESS_STATUS_ID},
	).Decode(&sysData)

	if err != nil {
		if err.Error() == "mongo: no documents in result" {
			golog.Info("No existing sysdata found, starting fresh", "processId", PROCESS_STATUS_ID)
			if err := GetTokenAndWatch(watchCtx, watchCancel); err != nil {
				GracefulExit(err)
			}
		} else {
			golog.Error("Failed to get sysdata", "error", err.Error(), "processId", PROCESS_STATUS_ID)
			GracefulExit(err)
		}
	} else {
		if err := handleTokenAndWatch(watchCtx, watchCancel, sysData, startTs); err != nil {
			GracefulExit(err)
		}
	}

	// Wait for context to be cancelled
	<-ctx.Done()
	// Clean up ProcessMonitor
	if gProcessMonitor != nil {
		gProcessMonitor.StopMonitoring()
	}
}
